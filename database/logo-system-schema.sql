-- Logo Upload System Database Schema
-- Extends the existing RapidDoc AI database with logo functionality

-- Create user_logos table for storing user-uploaded logos
CREATE TABLE IF NOT EXISTS public.user_logos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
    
    -- Logo metadata
    name TEXT NOT NULL, -- User-defined name for the logo
    description TEXT, -- Optional description
    
    -- File information
    file_name TEXT NOT NULL, -- Original filename
    file_size INTEGER NOT NULL, -- File size in bytes
    file_type TEXT NOT NULL CHECK (file_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/webp')),
    
    -- Storage information
    storage_path TEXT NOT NULL, -- Path in Supabase storage
    public_url TEXT NOT NULL, -- Public URL for accessing the logo
    
    -- Image properties
    width INTEGER NOT NULL, -- Image width in pixels
    height INTEGER NOT NULL, -- Image height in pixels
    aspect_ratio DECIMAL(10,4) GENERATED ALWAYS AS (CAST(width AS DECIMAL) / CAST(height AS DECIMAL)) STORED,
    
    -- Logo settings
    is_default BOOLEAN DEFAULT false, -- Whether this is the user's default logo
    is_active BOOLEAN DEFAULT true, -- Whether the logo is active/available
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0, -- How many times this logo has been used
    last_used_at TIMESTAMPTZ, -- When the logo was last used in a document
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_user_default_logo UNIQUE (user_id, is_default) DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT valid_file_size CHECK (file_size > 0 AND file_size <= 10485760), -- Max 10MB
    CONSTRAINT valid_dimensions CHECK (width > 0 AND height > 0)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_logos_user_id ON public.user_logos(user_id);
CREATE INDEX IF NOT EXISTS idx_user_logos_is_default ON public.user_logos(user_id, is_default) WHERE is_default = true;
CREATE INDEX IF NOT EXISTS idx_user_logos_is_active ON public.user_logos(user_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_logos_usage ON public.user_logos(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_user_logos_created_at ON public.user_logos(created_at DESC);

-- Add logo-related columns to user_profiles table
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS default_logo_id UUID REFERENCES public.user_logos(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS logo_storage_used_mb INTEGER DEFAULT 0; -- Track logo storage usage separately

-- Add logo-related columns to projects table (inferred structure)
-- Note: This assumes the projects table exists with the structure used in projectsService.js
DO $$ 
BEGIN
    -- Add logo_id column to projects table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects' AND table_schema = 'public') THEN
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS logo_id UUID REFERENCES public.user_logos(id) ON DELETE SET NULL;
        
        -- Create index for logo_id
        CREATE INDEX IF NOT EXISTS idx_projects_logo_id ON public.projects(logo_id);
    END IF;
END $$;

-- Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_user_logos_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_user_logos_updated_at
    BEFORE UPDATE ON public.user_logos
    FOR EACH ROW
    EXECUTE FUNCTION public.update_user_logos_updated_at();

-- Create function to ensure only one default logo per user
CREATE OR REPLACE FUNCTION public.ensure_single_default_logo()
RETURNS TRIGGER AS $$
BEGIN
    -- If setting a logo as default, unset all other default logos for this user
    IF NEW.is_default = true THEN
        UPDATE public.user_logos 
        SET is_default = false 
        WHERE user_id = NEW.user_id 
        AND id != NEW.id 
        AND is_default = true;
        
        -- Update user_profiles default_logo_id
        UPDATE public.user_profiles 
        SET default_logo_id = NEW.id 
        WHERE id = NEW.user_id;
    END IF;
    
    -- If unsetting default, clear user_profiles default_logo_id if it matches
    IF OLD.is_default = true AND NEW.is_default = false THEN
        UPDATE public.user_profiles 
        SET default_logo_id = NULL 
        WHERE id = NEW.user_id AND default_logo_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for default logo management
CREATE TRIGGER ensure_single_default_logo_trigger
    AFTER INSERT OR UPDATE ON public.user_logos
    FOR EACH ROW
    EXECUTE FUNCTION public.ensure_single_default_logo();

-- Create function to update logo storage usage
CREATE OR REPLACE FUNCTION public.update_logo_storage_usage()
RETURNS TRIGGER AS $$
DECLARE
    user_uuid UUID;
    total_storage INTEGER;
BEGIN
    -- Determine user_id based on operation
    IF TG_OP = 'DELETE' THEN
        user_uuid := OLD.user_id;
    ELSE
        user_uuid := NEW.user_id;
    END IF;
    
    -- Calculate total logo storage for user
    SELECT COALESCE(SUM(file_size), 0) / (1024 * 1024) -- Convert to MB
    INTO total_storage
    FROM public.user_logos
    WHERE user_id = user_uuid AND is_active = true;
    
    -- Update user_profiles with new storage usage
    UPDATE public.user_profiles
    SET logo_storage_used_mb = total_storage
    WHERE id = user_uuid;
    
    -- Return appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for storage usage tracking
CREATE TRIGGER update_logo_storage_usage_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.user_logos
    FOR EACH ROW
    EXECUTE FUNCTION public.update_logo_storage_usage();

-- Create function to increment logo usage count
CREATE OR REPLACE FUNCTION public.increment_logo_usage(logo_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_logos
    SET usage_count = usage_count + 1,
        last_used_at = NOW()
    WHERE id = logo_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Row Level Security (RLS) Policies
ALTER TABLE public.user_logos ENABLE ROW LEVEL SECURITY;

-- Users can view their own logos
CREATE POLICY "Users can view their own logos" ON public.user_logos
    FOR SELECT USING (user_id = auth.user_id());

-- Users can create their own logos
CREATE POLICY "Users can create their own logos" ON public.user_logos
    FOR INSERT WITH CHECK (user_id = auth.user_id());

-- Users can update their own logos
CREATE POLICY "Users can update their own logos" ON public.user_logos
    FOR UPDATE USING (user_id = auth.user_id());

-- Users can delete their own logos
CREATE POLICY "Users can delete their own logos" ON public.user_logos
    FOR DELETE USING (user_id = auth.user_id());

-- Create storage bucket for user logos
INSERT INTO storage.buckets (id, name, public) 
VALUES ('user-logos', 'user-logos', true)
ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for user logos bucket
CREATE POLICY "Users can upload their own logos" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'user-logos' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own logos" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'user-logos' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own logos" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'user-logos' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own logos" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'user-logos' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );
