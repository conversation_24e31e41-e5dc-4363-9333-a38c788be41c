import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';
import LogoUpload from '../../../components/ui/LogoUpload';
import { useAuth } from '../../../contexts/AuthContext';
import { getUserLogos, setDefaultLogo, deleteLogo, uploadLogo } from '../../../services/logoService';
import { prodLogger } from '../../../utils/prodLogger.js';

/**
 * Branding Section - Central Logo Management
 * Provides centralized logo management in user account settings
 */
const BrandingSection = () => {
  const { user, profile, updateProfile } = useAuth();
  const [logos, setLogos] = useState([]);
  const [defaultLogo, setDefaultLogoState] = useState(null);
  const [loading, setLoading] = useState(false);
  const [uploadMode, setUploadMode] = useState(false);
  const [selectedLogos, setSelectedLogos] = useState(new Set());
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Load user's logos on component mount
  useEffect(() => {
    if (user?.id) {
      loadUserLogos();
    }
  }, [user?.id]);

  const loadUserLogos = async () => {
    try {
      setLoading(true);
      setError('');
      
      const result = await getUserLogos(user.id, { 
        activeOnly: true, 
        includeDefault: true 
      });
      
      if (result.success) {
        setLogos(result.logos);
        setDefaultLogoState(result.defaultLogo);
      } else {
        setError(result.error || 'Failed to load logos');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to load user logos:', err);
      setError('Failed to load logos');
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = async (file, validationResult) => {
    if (!validationResult.success) {
      setError(validationResult.error);
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const uploadResult = await uploadLogo(file, user.id, {
        name: file.name.replace(/\.[^/.]+$/, ''),
        description: 'Logo uploaded from account settings'
      });
      
      if (uploadResult.success) {
        setSuccessMessage('Logo uploaded successfully!');
        setUploadMode(false);
        await loadUserLogos(); // Refresh the list
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        setError(uploadResult.error || 'Failed to upload logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to upload logo:', err);
      setError('Failed to upload logo');
    } finally {
      setLoading(false);
    }
  };

  const handleSetDefault = async (logoId) => {
    try {
      setLoading(true);
      setError('');
      
      const result = await setDefaultLogo(logoId, user.id);
      
      if (result.success) {
        setSuccessMessage('Default logo updated!');
        await loadUserLogos(); // Refresh to show new default
        
        // Update user profile context
        await updateProfile({ default_logo_id: logoId });
        
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        setError(result.error || 'Failed to set default logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to set default logo:', err);
      setError('Failed to set default logo');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLogo = async (logoId) => {
    if (!confirm('Are you sure you want to delete this logo? This action cannot be undone.')) {
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const result = await deleteLogo(logoId, user.id);
      
      if (result.success) {
        setSuccessMessage('Logo deleted successfully!');
        await loadUserLogos(); // Refresh the list
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        setError(result.error || 'Failed to delete logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to delete logo:', err);
      setError('Failed to delete logo');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedLogos.size === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedLogos.size} logo(s)? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const deletePromises = Array.from(selectedLogos).map(logoId => 
        deleteLogo(logoId, user.id)
      );
      
      await Promise.all(deletePromises);
      
      setSuccessMessage(`${selectedLogos.size} logo(s) deleted successfully!`);
      setSelectedLogos(new Set());
      await loadUserLogos();
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      prodLogger.error('❌ Failed to delete logos:', err);
      setError('Failed to delete selected logos');
    } finally {
      setLoading(false);
    }
  };

  const toggleLogoSelection = (logoId) => {
    const newSelection = new Set(selectedLogos);
    if (newSelection.has(logoId)) {
      newSelection.delete(logoId);
    } else {
      newSelection.add(logoId);
    }
    setSelectedLogos(newSelection);
  };

  const formatFileSize = (bytes) => {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  };

  const formatUsageCount = (count) => {
    if (count === 0) return 'Never used';
    if (count === 1) return '1 time';
    return `${count} times`;
  };

  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-text-primary mb-2">Brand Management</h2>
        <p className="text-sm text-text-secondary">
          Manage your logos and branding assets. Set a default logo to automatically apply to new documents.
        </p>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-600">
          {error}
        </div>
      )}
      
      {successMessage && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg text-sm text-green-600">
          {successMessage}
        </div>
      )}

      {/* Default Logo Display */}
      {defaultLogo && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-4">
            <img 
              src={defaultLogo.previewUrl} 
              alt={defaultLogo.name}
              className="w-16 h-16 object-contain bg-white rounded border"
            />
            <div className="flex-1">
              <h3 className="font-medium text-blue-900">Default Logo</h3>
              <p className="text-sm text-blue-700">{defaultLogo.name}</p>
              <p className="text-xs text-blue-600">
                This logo will be automatically applied to new documents
              </p>
            </div>
            <Icon name="Star" size={20} className="text-blue-500" />
          </div>
        </div>
      )}

      {/* Action Bar */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Button
            variant="primary"
            onClick={() => setUploadMode(true)}
            iconName="Plus"
            iconPosition="left"
            disabled={loading}
          >
            Upload Logo
          </Button>
          
          {selectedLogos.size > 0 && (
            <Button
              variant="ghost"
              onClick={handleBulkDelete}
              iconName="Trash2"
              iconPosition="left"
              disabled={loading}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Delete Selected ({selectedLogos.size})
            </Button>
          )}
        </div>
        
        <div className="text-sm text-text-secondary">
          {logos.length} logo{logos.length !== 1 ? 's' : ''} • {formatFileSize(profile?.logo_storage_used_mb * 1024 * 1024 || 0)} used
        </div>
      </div>

      {/* Upload Interface */}
      {uploadMode && (
        <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">Upload New Logo</h3>
            <Button
              variant="ghost"
              onClick={() => setUploadMode(false)}
              iconName="X"
              size="sm"
            >
              Cancel
            </Button>
          </div>
          
          <LogoUpload
            onFileSelect={handleLogoUpload}
            disabled={loading}
            showPreview={true}
          />
        </div>
      )}

      {/* Logo Grid */}
      {loading && logos.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size={32} className="animate-spin text-gray-400" />
        </div>
      ) : logos.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {logos.map((logo) => (
            <div
              key={logo.id}
              className={`relative border-2 rounded-lg p-4 transition-all hover:shadow-md ${
                selectedLogos.has(logo.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {/* Selection Checkbox */}
              <div className="absolute top-2 left-2">
                <input
                  type="checkbox"
                  checked={selectedLogos.has(logo.id)}
                  onChange={() => toggleLogoSelection(logo.id)}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
              </div>

              {/* Default Badge */}
              {logo.isDefault && (
                <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                  Default
                </div>
              )}

              {/* Logo Preview */}
              <div className="mb-3 mt-2">
                <img
                  src={logo.previewUrl}
                  alt={logo.name}
                  className="w-full h-24 object-contain bg-white rounded border"
                />
              </div>

              {/* Logo Info */}
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 truncate">{logo.name}</h4>
                <div className="text-xs text-gray-500 space-y-1">
                  <p>{logo.dimensions}</p>
                  <p>{logo.fileSize}</p>
                  <p>Used {formatUsageCount(logo.usageCount)}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-3 flex gap-2">
                {!logo.isDefault && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSetDefault(logo.id)}
                    disabled={loading}
                    className="flex-1 text-xs"
                  >
                    Set Default
                  </Button>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteLogo(logo.id)}
                  disabled={loading}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  iconName="Trash2"
                />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Icon name="Image" size={48} className="mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No logos yet</h3>
          <p className="text-gray-500 mb-4">Upload your first logo to get started with branding your documents.</p>
          <Button
            variant="primary"
            onClick={() => setUploadMode(true)}
            iconName="Plus"
            iconPosition="left"
          >
            Upload Your First Logo
          </Button>
        </div>
      )}
    </div>
  );
};

export default BrandingSection;
