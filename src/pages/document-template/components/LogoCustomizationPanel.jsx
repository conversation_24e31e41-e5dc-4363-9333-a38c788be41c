import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';
import LogoUpload from '../../../components/ui/LogoUpload';
import { getLogosForDocumentCreation } from '../../../services/logoIntegrationService';
import { prepareLogoForTemplate, getLogoPositionOptions, validateLogoTemplateCompatibility } from '../../../services/templateLogoService';
import { uploadLogo } from '../../../services/logoService';
import { useAuth } from '../../../hooks/useAuth';
import { prodLogger } from '../../../utils/prodLogger.js';

/**
 * Logo Customization Panel
 * Integrated into Cover Preview Interface for template-specific logo customization
 */
const LogoCustomizationPanel = ({
  template,
  selectedLogoId = null,
  logoSettings = {},
  onLogoChange = null,
  onSettingsChange = null,
  className = ""
}) => {
  const { user } = useAuth();
  const [isExpanded, setIsExpanded] = useState(false);
  const [userLogos, setUserLogos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadMode, setUploadMode] = useState(false);
  const [selectedLogo, setSelectedLogo] = useState(null);
  const [currentSettings, setCurrentSettings] = useState({
    position: 'top-right',
    size: 'medium',
    opacity: 1.0,
    ...logoSettings
  });
  const [validation, setValidation] = useState(null);

  // Load user's logos on component mount
  useEffect(() => {
    if (user?.id) {
      loadUserLogos();
    }
  }, [user?.id]);

  // Update settings when template changes
  useEffect(() => {
    if (template && selectedLogo) {
      const positionOptions = getLogoPositionOptions(template);
      const defaultPosition = positionOptions.find(opt => opt.isDefault)?.value || 'top-right';
      
      setCurrentSettings(prev => ({
        ...prev,
        position: prev.position || defaultPosition
      }));
      
      // Validate logo compatibility with new template
      const compatibility = validateLogoTemplateCompatibility(selectedLogo, template);
      setValidation(compatibility);
    }
  }, [template, selectedLogo]);

  const loadUserLogos = async () => {
    try {
      setLoading(true);
      const result = await getLogosForDocumentCreation(user.id);
      
      if (result.success) {
        setUserLogos(result.logos);
        
        // Auto-select default logo if no logo is currently selected
        if (!selectedLogoId && result.defaultLogo) {
          handleLogoSelect(result.defaultLogo.id);
        }
      }
    } catch (error) {
      prodLogger.error('❌ Failed to load user logos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogoSelect = async (logoId) => {
    try {
      setLoading(true);
      
      // Prepare logo for template integration
      const logoData = await prepareLogoForTemplate(user.id, template, logoId, currentSettings);
      
      if (logoData) {
        setSelectedLogo(logoData);
        setCurrentSettings(logoData.settings);
        
        // Validate compatibility
        const compatibility = validateLogoTemplateCompatibility(logoData, template);
        setValidation(compatibility);
        
        // Notify parent component
        if (onLogoChange) {
          onLogoChange(logoData);
        }
      }
    } catch (error) {
      prodLogger.error('❌ Failed to select logo:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (setting, value) => {
    const newSettings = { ...currentSettings, [setting]: value };
    setCurrentSettings(newSettings);
    
    // Update logo data with new settings
    if (selectedLogo) {
      const updatedLogo = {
        ...selectedLogo,
        settings: { ...selectedLogo.settings, ...newSettings }
      };
      setSelectedLogo(updatedLogo);
      
      if (onLogoChange) {
        onLogoChange(updatedLogo);
      }
    }
    
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    }
  };

  const handleLogoUpload = async (file, validationResult) => {
    if (!validationResult.success) return;
    
    try {
      setLoading(true);
      
      const uploadResult = await uploadLogo(file, user.id, {
        name: file.name.replace(/\.[^/.]+$/, ''),
        description: `Logo uploaded for ${template?.name || 'template'} customization`
      });
      
      if (uploadResult.success) {
        // Refresh logo list
        await loadUserLogos();
        
        // Auto-select the newly uploaded logo
        await handleLogoSelect(uploadResult.logo.id);
        
        // Exit upload mode
        setUploadMode(false);
        
        prodLogger.debug('✅ Logo uploaded and selected successfully');
      }
    } catch (error) {
      prodLogger.error('❌ Failed to upload logo:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveLogo = () => {
    setSelectedLogo(null);
    setValidation(null);
    
    if (onLogoChange) {
      onLogoChange(null);
    }
  };

  const positionOptions = template ? getLogoPositionOptions(template) : [];

  return (
    <div className={`logo-customization-panel bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Panel Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <Icon name="Image" size={20} className="text-gray-600" />
          <div>
            <h3 className="font-medium text-gray-900">Logo Customization</h3>
            <p className="text-sm text-gray-500">
              {selectedLogo ? `${selectedLogo.name} • ${currentSettings.position}` : 'Add your logo to the template'}
            </p>
          </div>
        </div>
        <Icon 
          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
          size={20} 
          className="text-gray-400" 
        />
      </div>

      {/* Panel Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-4 space-y-4">
          {/* Logo Selection */}
          {!uploadMode ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Select Logo</label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setUploadMode(true)}
                  iconName="Plus"
                  iconPosition="left"
                >
                  Upload New
                </Button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Icon name="Loader2" size={24} className="animate-spin text-gray-400" />
                </div>
              ) : userLogos.length > 0 ? (
                <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {userLogos.map((logo) => (
                    <div
                      key={logo.id}
                      className={`relative border-2 rounded-lg p-2 cursor-pointer transition-all hover:shadow-md ${
                        selectedLogo?.id === logo.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleLogoSelect(logo.id)}
                    >
                      <img
                        src={logo.previewUrl}
                        alt={logo.name}
                        className="w-full h-12 object-contain"
                      />
                      <p className="text-xs text-gray-600 mt-1 truncate">{logo.name}</p>
                      {logo.isDefault && (
                        <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs px-1 rounded">
                          Default
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <Icon name="Image" size={32} className="mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">No logos found</p>
                  <p className="text-xs">Upload your first logo to get started</p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Upload Logo</label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setUploadMode(false)}
                  iconName="X"
                >
                  Cancel
                </Button>
              </div>
              
              <LogoUpload
                onFileSelect={handleLogoUpload}
                disabled={loading}
                showPreview={true}
                className="border-dashed"
              />
            </div>
          )}

          {/* Logo Settings */}
          {selectedLogo && (
            <div className="space-y-4 pt-4 border-t border-gray-100">
              {/* Position Setting */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Position</label>
                <div className="grid grid-cols-3 gap-2">
                  {positionOptions.map((option) => (
                    <button
                      key={option.value}
                      className={`flex items-center justify-center gap-1 px-3 py-2 text-xs border rounded-md transition-all ${
                        currentSettings.position === option.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 text-gray-600'
                      }`}
                      onClick={() => handleSettingChange('position', option.value)}
                    >
                      <Icon name={option.icon} size={12} />
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Size Setting */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Size</label>
                <div className="grid grid-cols-3 gap-2">
                  {['small', 'medium', 'large'].map((size) => (
                    <button
                      key={size}
                      className={`px-3 py-2 text-xs border rounded-md transition-all capitalize ${
                        currentSettings.size === size
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 text-gray-600'
                      }`}
                      onClick={() => handleSettingChange('size', size)}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Opacity Setting */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Opacity: {Math.round(currentSettings.opacity * 100)}%
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={currentSettings.opacity}
                  onChange={(e) => handleSettingChange('opacity', parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Remove Logo */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemoveLogo}
                iconName="Trash2"
                iconPosition="left"
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                Remove Logo
              </Button>
            </div>
          )}

          {/* Validation Messages */}
          {validation && (validation.warnings.length > 0 || validation.recommendations.length > 0) && (
            <div className="space-y-2 pt-4 border-t border-gray-100">
              {validation.warnings.map((warning, index) => (
                <div key={index} className="flex items-start gap-2 text-sm text-yellow-600">
                  <Icon name="AlertTriangle" size={16} className="mt-0.5 flex-shrink-0" />
                  <span>{warning}</span>
                </div>
              ))}
              {validation.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start gap-2 text-sm text-blue-600">
                  <Icon name="Lightbulb" size={16} className="mt-0.5 flex-shrink-0" />
                  <span>{recommendation}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LogoCustomizationPanel;
