/**
 * Font Loader Tests
 * Tests for the font loading utility
 */

import fontLoader from '../fontLoader.js';

// Mock DOM APIs
global.document = {
  createElement: jest.fn(() => ({
    href: '',
    rel: '',
    onload: null,
    onerror: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  })),
  head: {
    appendChild: jest.fn(),
    querySelector: jest.fn(() => null)
  },
  querySelector: jest.fn(() => null),
  fonts: {
    load: jest.fn(() => Promise.resolve()),
    check: jest.fn(() => true)
  }
};

describe('FontLoader', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    fontLoader.clearCache();
  });

  describe('isSystemFont', () => {
    it('should identify system fonts correctly', () => {
      expect(fontLoader.isSystemFont('Arial')).toBe(true);
      expect(fontLoader.isSystemFont('Helvetica')).toBe(true);
      expect(fontLoader.isSystemFont('Times New Roman')).toBe(true);
      expect(fontLoader.isSystemFont('Georgia')).toBe(true);
      expect(fontLoader.isSystemFont('Custom Font')).toBe(false);
      expect(fontLoader.isSystemFont('Roboto')).toBe(false);
    });
  });

  describe('isFontAvailable', () => {
    it('should return true for system fonts', () => {
      expect(fontLoader.isFontAvailable('Arial')).toBe(true);
      expect(fontLoader.isFontAvailable('Georgia')).toBe(true);
    });

    it('should check document.fonts for non-system fonts', () => {
      document.fonts.check.mockReturnValue(true);
      expect(fontLoader.isFontAvailable('Roboto')).toBe(true);
      expect(document.fonts.check).toHaveBeenCalledWith('16px "Roboto"');
    });

    it('should handle fonts.check errors gracefully', () => {
      document.fonts.check.mockImplementation(() => {
        throw new Error('Font check failed');
      });
      expect(fontLoader.isFontAvailable('Roboto')).toBe(false);
    });
  });

  describe('getFontWithFallbacks', () => {
    it('should return system fonts as-is', () => {
      expect(fontLoader.getFontWithFallbacks('Arial')).toBe('Arial');
      expect(fontLoader.getFontWithFallbacks('Georgia')).toBe('Georgia');
    });

    it('should add appropriate fallbacks for custom fonts', () => {
      expect(fontLoader.getFontWithFallbacks('Roboto')).toBe('"Roboto", Arial, Helvetica, sans-serif');
      expect(fontLoader.getFontWithFallbacks('Playfair Display')).toBe('"Playfair Display", Georgia, "Times New Roman", serif');
      expect(fontLoader.getFontWithFallbacks('JetBrains Mono')).toBe('"JetBrains Mono", "Courier New", Courier, monospace');
    });
  });

  describe('loadGoogleFont', () => {
    it('should not load system fonts', async () => {
      await fontLoader.loadGoogleFont('Arial');
      expect(document.createElement).not.toHaveBeenCalled();
    });

    it('should load Google fonts', async () => {
      const mockLink = {
        href: '',
        rel: '',
        onload: null,
        onerror: null
      };
      document.createElement.mockReturnValue(mockLink);
      document.fonts.load.mockResolvedValue();

      const loadPromise = fontLoader.loadGoogleFont('Roboto');
      
      // Simulate successful load
      setTimeout(() => {
        if (mockLink.onload) mockLink.onload();
      }, 0);

      await loadPromise;

      expect(document.createElement).toHaveBeenCalledWith('link');
      expect(mockLink.href).toBe('https://fonts.googleapis.com/css2?family=Roboto:wght@400&display=swap');
      expect(mockLink.rel).toBe('stylesheet');
      expect(document.head.appendChild).toHaveBeenCalledWith(mockLink);
    });

    it('should handle font loading errors', async () => {
      const mockLink = {
        href: '',
        rel: '',
        onload: null,
        onerror: null
      };
      document.createElement.mockReturnValue(mockLink);

      const loadPromise = fontLoader.loadGoogleFont('InvalidFont');
      
      // Simulate error
      setTimeout(() => {
        if (mockLink.onerror) mockLink.onerror();
      }, 0);

      await expect(loadPromise).rejects.toThrow('Failed to load font: InvalidFont');
    });

    it('should not load the same font twice', async () => {
      const mockLink = {
        href: '',
        rel: '',
        onload: null,
        onerror: null
      };
      document.createElement.mockReturnValue(mockLink);
      document.fonts.load.mockResolvedValue();

      // First load
      const loadPromise1 = fontLoader.loadGoogleFont('Roboto');
      setTimeout(() => {
        if (mockLink.onload) mockLink.onload();
      }, 0);
      await loadPromise1;

      // Second load should not create new link
      document.createElement.mockClear();
      await fontLoader.loadGoogleFont('Roboto');
      expect(document.createElement).not.toHaveBeenCalled();
    });
  });

  describe('ensureFontLoaded', () => {
    it('should return font family if already available', async () => {
      const result = await fontLoader.ensureFontLoaded('Arial');
      expect(result).toBe('Arial');
    });

    it('should load Google fonts and return font family', async () => {
      const mockLink = {
        href: '',
        rel: '',
        onload: null,
        onerror: null
      };
      document.createElement.mockReturnValue(mockLink);
      document.fonts.load.mockResolvedValue();

      const loadPromise = fontLoader.ensureFontLoaded('Roboto');
      
      setTimeout(() => {
        if (mockLink.onload) mockLink.onload();
      }, 0);

      const result = await loadPromise;
      expect(result).toBe('Roboto');
    });

    it('should return fallback on loading error', async () => {
      const mockLink = {
        href: '',
        rel: '',
        onload: null,
        onerror: null
      };
      document.createElement.mockReturnValue(mockLink);

      const loadPromise = fontLoader.ensureFontLoaded('InvalidFont', { fallback: 'Arial' });
      
      setTimeout(() => {
        if (mockLink.onerror) mockLink.onerror();
      }, 0);

      const result = await loadPromise;
      expect(result).toBe('Arial');
    });

    it('should not load fonts when loadGoogleFonts is false', async () => {
      const result = await fontLoader.ensureFontLoaded('Roboto', { 
        loadGoogleFonts: false, 
        fallback: 'Arial' 
      });
      expect(result).toBe('Arial');
      expect(document.createElement).not.toHaveBeenCalled();
    });
  });

  describe('loadCustomFont', () => {
    it('should validate Google Fonts URLs', async () => {
      await expect(fontLoader.loadCustomFont('https://example.com/font.css', 'TestFont'))
        .rejects.toThrow('Only Google Fonts URLs are supported');
    });

    it('should validate URL format', async () => {
      await expect(fontLoader.loadCustomFont('invalid-url', 'TestFont'))
        .rejects.toThrow('Invalid font URL format');
    });

    it('should load custom Google fonts', async () => {
      const mockLink = {
        href: '',
        rel: '',
        onload: null,
        onerror: null
      };
      document.createElement.mockReturnValue(mockLink);
      document.fonts.load.mockResolvedValue();

      const loadPromise = fontLoader.loadCustomFont(
        'https://fonts.googleapis.com/css2?family=Custom+Font:wght@400&display=swap',
        'Custom Font'
      );
      
      setTimeout(() => {
        if (mockLink.onload) mockLink.onload();
      }, 0);

      await loadPromise;

      expect(document.createElement).toHaveBeenCalledWith('link');
      expect(mockLink.href).toBe('https://fonts.googleapis.com/css2?family=Custom+Font:wght@400&display=swap');
      expect(document.head.appendChild).toHaveBeenCalledWith(mockLink);
    });
  });
});
