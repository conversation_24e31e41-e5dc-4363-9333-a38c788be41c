/**
 * Font Loading Utility for DocForge AI
 * Handles dynamic font loading for canvas rendering and UI components
 * with caching, validation, and fallback support
 */

import errorMonitor, { ErrorSeverity } from "./errorMonitor.js";

import { prodLogger } from './prodLogger.js';
/**
 * Font Loading Service Class
 * Manages font loading, caching, and validation
 */
class FontLoader {
  constructor() {
    this.loadedFonts = new Set();
    this.loadingPromises = new Map();
    this.fontCache = new Map();
    this.loadTimeout = 10000; // 10 seconds
  }

  /**
   * Check if a font is available in the system or already loaded
   * @param {string} fontFamily - Font family name
   * @returns {boolean} Whether the font is available
   */
  isFontAvailable(fontFamily) {
    // Check if it's a system font or already loaded
    if (this.isSystemFont(fontFamily) || this.loadedFonts.has(fontFamily)) {
      return true;
    }

    // Use Font Loading API if available
    if ('fonts' in document) {
      try {
        return document.fonts.check(`16px "${fontFamily}"`);
      } catch (error) {
        prodLogger.warn(`Font check failed for ${fontFamily}:`, error);
        return false;
      }
    }

    return false;
  }

  /**
   * Check if a font is a system font
   * @param {string} fontFamily - Font family name
   * @returns {boolean} Whether it's a system font
   */
  isSystemFont(fontFamily) {
    const systemFonts = [
      'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 
      'Verdana', 'Courier New', 'Impact', 'Comic Sans MS',
      'Trebuchet MS', 'Arial Black', 'Palatino', 'Garamond',
      'Bookman', 'Avant Garde', 'sans-serif', 'serif', 'monospace'
    ];
    
    return systemFonts.some(sysFont => 
      fontFamily.toLowerCase().includes(sysFont.toLowerCase())
    );
  }

  /**
   * Load a Google Font
   * @param {string} fontFamily - Font family name
   * @param {Object} options - Loading options
   * @returns {Promise<void>} Promise that resolves when font is loaded
   */
  async loadGoogleFont(fontFamily, options = {}) {
    const { weights = [400], display = 'swap' } = options;

    // Check if already loaded
    if (this.loadedFonts.has(fontFamily)) {
      return Promise.resolve();
    }

    // Check if loading is in progress
    if (this.loadingPromises.has(fontFamily)) {
      return this.loadingPromises.get(fontFamily);
    }

    // Create loading promise
    const loadingPromise = this._loadGoogleFontInternal(fontFamily, weights, display);
    this.loadingPromises.set(fontFamily, loadingPromise);

    try {
      await loadingPromise;
      this.loadedFonts.add(fontFamily);
      this.loadingPromises.delete(fontFamily);
    } catch (error) {
      this.loadingPromises.delete(fontFamily);
      throw error;
    }
  }

  /**
   * Internal Google Font loading implementation
   * @private
   */
  async _loadGoogleFontInternal(fontFamily, weights, display) {
    const weightsParam = weights.join(';');
    const fontUrl = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(/\s+/g, '+')}:wght@${weightsParam}&display=${display}`;

    // Check if font link already exists
    if (document.querySelector(`link[href="${fontUrl}"]`)) {
      return this._waitForFontLoad(fontFamily);
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.href = fontUrl;
      link.rel = 'stylesheet';

      const timeout = setTimeout(() => {
        reject(new Error(`Font loading timeout for ${fontFamily}`));
      }, this.loadTimeout);

      link.onload = async () => {
        clearTimeout(timeout);
        try {
          await this._waitForFontLoad(fontFamily);
          resolve();
        } catch (error) {
          reject(error);
        }
      };

      link.onerror = () => {
        clearTimeout(timeout);
        reject(new Error(`Failed to load font: ${fontFamily}`));
      };

      document.head.appendChild(link);
    });
  }

  /**
   * Wait for font to be available using Font Loading API
   * @private
   */
  async _waitForFontLoad(fontFamily) {
    if ('fonts' in document) {
      try {
        await document.fonts.load(`16px "${fontFamily}"`);
        
        // Additional check to ensure font is really available
        const maxWait = 3000; // 3 seconds
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWait) {
          if (document.fonts.check(`16px "${fontFamily}"`)) {
            return;
          }
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error(`Font not available after loading: ${fontFamily}`);
      } catch (error) {
        prodLogger.warn(`Font Loading API failed for ${fontFamily}:`, error);
        // Fallback to simple timeout
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } else {
      // Fallback for browsers without Font Loading API
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  /**
   * Load a custom font from URL
   * @param {string} fontUrl - Font CSS URL
   * @param {string} fontFamily - Font family name
   * @returns {Promise<void>} Promise that resolves when font is loaded
   */
  async loadCustomFont(fontUrl, fontFamily) {
    // Validate URL
    if (!fontUrl.includes('fonts.googleapis.com')) {
      throw new Error('Only Google Fonts URLs are supported');
    }

    try {
      new URL(fontUrl);
    } catch (error) {
      throw new Error('Invalid font URL format');
    }

    // Check if already loaded
    if (this.loadedFonts.has(fontFamily)) {
      return Promise.resolve();
    }

    // Check if loading is in progress
    if (this.loadingPromises.has(fontFamily)) {
      return this.loadingPromises.get(fontFamily);
    }

    const loadingPromise = this._loadCustomFontInternal(fontUrl, fontFamily);
    this.loadingPromises.set(fontFamily, loadingPromise);

    try {
      await loadingPromise;
      this.loadedFonts.add(fontFamily);
      this.loadingPromises.delete(fontFamily);
    } catch (error) {
      this.loadingPromises.delete(fontFamily);
      throw error;
    }
  }

  /**
   * Internal custom font loading implementation
   * @private
   */
  async _loadCustomFontInternal(fontUrl, fontFamily) {
    // Check if font link already exists
    if (document.querySelector(`link[href="${fontUrl}"]`)) {
      return this._waitForFontLoad(fontFamily);
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.href = fontUrl;
      link.rel = 'stylesheet';

      const timeout = setTimeout(() => {
        reject(new Error(`Font loading timeout for ${fontFamily}`));
      }, this.loadTimeout);

      link.onload = async () => {
        clearTimeout(timeout);
        try {
          await this._waitForFontLoad(fontFamily);
          resolve();
        } catch (error) {
          reject(error);
        }
      };

      link.onerror = () => {
        clearTimeout(timeout);
        reject(new Error(`Failed to load custom font: ${fontFamily}`));
      };

      document.head.appendChild(link);
    });
  }

  /**
   * Ensure a font is loaded before use
   * @param {string} fontFamily - Font family name
   * @param {Object} options - Loading options
   * @returns {Promise<string>} Promise that resolves to the font family or fallback
   */
  async ensureFontLoaded(fontFamily, options = {}) {
    const { fallback = 'Arial', loadGoogleFonts = true } = options;

    try {
      // Check if it's already available
      if (this.isFontAvailable(fontFamily)) {
        return fontFamily;
      }

      // Don't try to load system fonts
      if (this.isSystemFont(fontFamily)) {
        return fontFamily;
      }

      // Try to load as Google Font if enabled
      if (loadGoogleFonts) {
        await this.loadGoogleFont(fontFamily);
        return fontFamily;
      }

      // If we can't load it, return fallback
      prodLogger.warn(`Font not available and loading disabled: ${fontFamily}`);
      return fallback;

    } catch (error) {
      prodLogger.warn(`Failed to load font ${fontFamily}:`, error);
      errorMonitor.captureError(
        error,
        { fontFamily, fallback },
        ErrorSeverity.LOW
      );
      return fallback;
    }
  }

  /**
   * Get font fallback chain
   * @param {string} fontFamily - Primary font family
   * @returns {string} Font family with fallbacks
   */
  getFontWithFallbacks(fontFamily) {
    const fallbacks = {
      'serif': 'Georgia, "Times New Roman", serif',
      'sans-serif': 'Arial, Helvetica, sans-serif',
      'monospace': '"Courier New", Courier, monospace'
    };

    // If it's already a system font, return as-is
    if (this.isSystemFont(fontFamily)) {
      return fontFamily;
    }

    // Add appropriate fallbacks based on font characteristics
    if (fontFamily.toLowerCase().includes('serif') && !fontFamily.toLowerCase().includes('sans')) {
      return `"${fontFamily}", ${fallbacks.serif}`;
    } else if (fontFamily.toLowerCase().includes('mono')) {
      return `"${fontFamily}", ${fallbacks.monospace}`;
    } else {
      return `"${fontFamily}", ${fallbacks['sans-serif']}`;
    }
  }

  /**
   * Clear font cache and reset state
   */
  clearCache() {
    this.loadedFonts.clear();
    this.loadingPromises.clear();
    this.fontCache.clear();
  }
}

// Create singleton instance
const fontLoader = new FontLoader();

export default fontLoader;

// Export utility functions for convenience
export const {
  isFontAvailable,
  isSystemFont,
  loadGoogleFont,
  loadCustomFont,
  ensureFontLoaded,
  getFontWithFallbacks,
  clearCache
} = fontLoader;
