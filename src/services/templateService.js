/**
 * Template Service for DocForge AI - Image Overlay Templates
 * Handles fetching and managing image overlay cover templates from Supabase
 *
 * Features:
 * - Fetch image overlay templates from cover_templates table
 * - Create new templates with validation
 * - Category and tag filtering
 * - Template caching for performance
 * - Error handling and retry logic
 * - Background image URL management
 * - Image upload and processing
 */

import { supabase } from '../lib/supabase.js';
import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor.js';
import notificationService, { createTemplateNotification } from './notificationService.js';
import { NOTIFICATION_TYPES } from './userNotificationService.js';

import { prodLogger } from '../utils/prodLogger.js';
// Cache for templates to avoid repeated API calls
let templateCache = {
  templates: null,
  lastFetch: null,
  categories: null
};

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Check if cache is still valid
 */
const isCacheValid = () => {
  return templateCache.lastFetch && 
         (Date.now() - templateCache.lastFetch) < CACHE_DURATION;
};

/**
 * Clear template cache
 */
export const clearTemplateCache = () => {
  templateCache = {
    templates: null,
    lastFetch: null,
    categories: null
  };
};

/**
 * Fetch all available cover templates from Supabase
 * @param {Object} options - Filtering and pagination options
 * @returns {Promise<Object>} Templates data with metadata
 */
export const fetchCoverTemplates = async (options = {}) => {
  try {
    const {
      category = null,
      tags = [],
      status = 'active',
      limit = 50,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
      useCache = true
    } = options;

    // Return cached data if valid and no specific filters
    if (useCache && isCacheValid() && !category && tags.length === 0) {
      prodLogger.debug('📋 Template Service: Using cached templates');
      return {
        success: true,
        templates: templateCache.templates,
        total: templateCache.templates?.length || 0,
        cached: true
      };
    }

    prodLogger.debug('📋 Template Service: Fetching templates from Supabase', {
      category,
      tags,
      status,
      limit,
      offset
    });

    // Build query for image overlay templates
    let query = supabase
      .from('cover_templates')
      .select(`
        id,
        name,
        description,
        category,
        tags,
        background_image_url,
        background_image_width,
        background_image_height,
        text_overlays,
        thumbnail_url,
        preview_url,
        supported_formats,
        usage_count,
        rating,
        status,
        is_premium,
        created_at,
        updated_at
      `)
      .eq('status', status)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (tags.length > 0) {
      query = query.overlaps('tags', tags);
    }

    // Apply pagination
    if (limit > 0) {
      query = query.range(offset, offset + limit - 1);
    }

    const { data: templates, error, count } = await query;

    if (error) {
      prodLogger.error('❌ Template Service: Failed to fetch templates:', error);
      errorMonitor.captureError(
        new Error(`Template fetch failed: ${error.message}`),
        { category, tags, status },
        ErrorSeverity.HIGH
      );
      
      return {
        success: false,
        error: error.message,
        templates: [],
        total: 0
      };
    }

    // Cache results if no specific filters
    if (!category && tags.length === 0) {
      templateCache.templates = templates;
      templateCache.lastFetch = Date.now();
    }

    prodLogger.debug(`✅ Template Service: Fetched ${templates.length} templates`);

    return {
      success: true,
      templates: templates || [],
      total: count || templates?.length || 0,
      cached: false
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Unexpected error:', error);
    errorMonitor.captureError(
      error,
      { operation: 'fetchCoverTemplates', options },
      ErrorSeverity.HIGH
    );

    return {
      success: false,
      error: error.message,
      templates: [],
      total: 0
    };
  }
};

/**
 * Fetch a specific template by ID
 * @param {string} templateId - Template ID to fetch
 * @returns {Promise<Object>} Template data
 */
export const fetchTemplateById = async (templateId) => {
  try {
    prodLogger.debug(`📋 Template Service: Fetching template ${templateId}`);

    const { data: template, error } = await supabase
      .from('cover_templates')
      .select('*')
      .eq('id', templateId)
      .eq('status', 'active')
      .single();

    if (error) {
      prodLogger.error('❌ Template Service: Failed to fetch template:', error);
      return {
        success: false,
        error: error.message,
        template: null
      };
    }

    if (!template) {
      return {
        success: false,
        error: 'Template not found',
        template: null
      };
    }

    prodLogger.debug(`✅ Template Service: Fetched template ${templateId}`);

    return {
      success: true,
      template
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Unexpected error:', error);
    errorMonitor.captureError(
      error,
      { operation: 'fetchTemplateById', templateId },
      ErrorSeverity.MEDIUM
    );

    return {
      success: false,
      error: error.message,
      template: null
    };
  }
};

/**
 * Fetch available template categories
 * @returns {Promise<Object>} Categories data
 */
export const fetchTemplateCategories = async () => {
  try {
    // Return cached categories if available
    if (templateCache.categories && isCacheValid()) {
      prodLogger.debug('📋 Template Service: Using cached categories');
      return {
        success: true,
        categories: templateCache.categories,
        cached: true
      };
    }

    prodLogger.debug('📋 Template Service: Fetching template categories');

    const { data: templates, error } = await supabase
      .from('cover_templates')
      .select('category')
      .eq('status', 'active');

    if (error) {
      prodLogger.error('❌ Template Service: Failed to fetch categories:', error);
      return {
        success: false,
        error: error.message,
        categories: []
      };
    }

    // Extract unique categories
    const categories = [...new Set(templates.map(t => t.category))].filter(Boolean);

    // Cache categories
    templateCache.categories = categories;

    prodLogger.debug(`✅ Template Service: Found ${categories.length} categories`);

    return {
      success: true,
      categories,
      cached: false
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Unexpected error:', error);
    return {
      success: false,
      error: error.message,
      categories: []
    };
  }
};

/**
 * Update template usage count
 * @param {string} templateId - Template ID to update
 * @returns {Promise<Object>} Update result
 */
export const incrementTemplateUsage = async (templateId) => {
  try {
    prodLogger.debug(`📋 Template Service: Incrementing usage for template ${templateId}`);

    const { error } = await supabase
      .from('cover_templates')
      .update({
        usage_count: supabase.raw('usage_count + 1'),
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId);

    if (error) {
      prodLogger.error('❌ Template Service: Failed to update usage:', error);
      return {
        success: false,
        error: error.message
      };
    }

    prodLogger.debug(`✅ Template Service: Updated usage for template ${templateId}`);

    // Clear cache to ensure fresh data on next fetch
    clearTemplateCache();

    return {
      success: true
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Unexpected error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Search templates by name or description
 * @param {string} searchTerm - Search term
 * @param {Object} options - Additional filtering options
 * @returns {Promise<Object>} Search results
 */
export const searchTemplates = async (searchTerm, options = {}) => {
  try {
    const {
      category = null,
      tags = [],
      limit = 20
    } = options;

    prodLogger.debug(`📋 Template Service: Searching templates for "${searchTerm}"`);

    let query = supabase
      .from('cover_templates')
      .select(`
        id,
        name,
        description,
        category,
        tags,
        background_image_url,
        background_image_width,
        background_image_height,
        text_overlays,
        thumbnail_url,
        preview_url,
        usage_count,
        rating,
        is_premium
      `)
      .eq('status', 'active')
      .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
      .order('usage_count', { ascending: false })
      .limit(limit);

    // Apply additional filters
    if (category) {
      query = query.eq('category', category);
    }

    if (tags.length > 0) {
      query = query.overlaps('tags', tags);
    }

    const { data: templates, error } = await query;

    if (error) {
      prodLogger.error('❌ Template Service: Search failed:', error);
      return {
        success: false,
        error: error.message,
        templates: []
      };
    }

    prodLogger.debug(`✅ Template Service: Found ${templates.length} templates matching "${searchTerm}"`);

    return {
      success: true,
      templates: templates || [],
      total: templates?.length || 0
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Search error:', error);
    return {
      success: false,
      error: error.message,
      templates: []
    };
  }
};

/**
 * Template Creation and Management Functions
 */

/**
 * Validate template data before creation
 * @param {Object} templateData - Template data to validate
 * @returns {Object} Validation result
 */
export const validateTemplateData = (templateData) => {
  const errors = [];

  // Required fields validation
  if (!templateData.name || typeof templateData.name !== 'string' || templateData.name.trim().length === 0) {
    errors.push('Template name is required');
  }

  if (!templateData.category || typeof templateData.category !== 'string' || templateData.category.trim().length === 0) {
    errors.push('Template category is required');
  }

  if (!templateData.background_image_url || typeof templateData.background_image_url !== 'string') {
    errors.push('Background image URL is required');
  }

  if (!templateData.background_image_width || !Number.isInteger(templateData.background_image_width) || templateData.background_image_width <= 0) {
    errors.push('Valid background image width is required');
  }

  if (!templateData.background_image_height || !Number.isInteger(templateData.background_image_height) || templateData.background_image_height <= 0) {
    errors.push('Valid background image height is required');
  }

  if (!templateData.text_overlays || typeof templateData.text_overlays !== 'object') {
    errors.push('Text overlays configuration is required');
  }

  // Optional fields validation
  if (templateData.name && templateData.name.length > 100) {
    errors.push('Template name must be 100 characters or less');
  }

  if (templateData.description && templateData.description.length > 500) {
    errors.push('Template description must be 500 characters or less');
  }

  if (templateData.tags && (!Array.isArray(templateData.tags) || templateData.tags.some(tag => typeof tag !== 'string'))) {
    errors.push('Tags must be an array of strings');
  }

  // Text overlays validation
  if (templateData.text_overlays && templateData.text_overlays.overlays) {
    if (!Array.isArray(templateData.text_overlays.overlays)) {
      errors.push('Text overlays must be an array');
    } else {
      templateData.text_overlays.overlays.forEach((overlay, index) => {
        if (!overlay.id || typeof overlay.id !== 'string') {
          errors.push(`Overlay ${index + 1}: ID is required`);
        }
        if (!overlay.position || typeof overlay.position !== 'object') {
          errors.push(`Overlay ${index + 1}: Position configuration is required`);
        }
        if (!overlay.styling || typeof overlay.styling !== 'object') {
          errors.push(`Overlay ${index + 1}: Styling configuration is required`);
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Upload background image to Supabase storage
 * @param {File} imageFile - Image file to upload
 * @param {string} templateId - Template ID for file naming
 * @returns {Promise<Object>} Upload result with URL and dimensions
 */
export const uploadBackgroundImage = async (imageFile, templateId) => {
  try {
    prodLogger.debug('📋 Template Service: Uploading background image', { templateId, fileName: imageFile.name });

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(imageFile.type)) {
      const errorNotification = createTemplateNotification(
        NOTIFICATION_TYPES.ERROR,
        'Invalid File Type',
        'Only JPEG, PNG, and WebP images are allowed for template backgrounds.',
        { duration: 6000 }
      );
      notificationService.add(errorNotification);

      return {
        success: false,
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      };
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > maxSize) {
      const errorNotification = createTemplateNotification(
        NOTIFICATION_TYPES.ERROR,
        'File Too Large',
        'Template background images must be smaller than 10MB.',
        { duration: 6000 }
      );
      notificationService.add(errorNotification);

      return {
        success: false,
        error: 'File size too large. Maximum size is 10MB.'
      };
    }

    // Generate unique filename
    const fileExtension = imageFile.name.split('.').pop();
    const fileName = `${templateId}-${Date.now()}.${fileExtension}`;

    // Upload to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('template-backgrounds')
      .upload(fileName, imageFile, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      prodLogger.error('❌ Template Service: Failed to upload image:', uploadError);

      const errorNotification = createTemplateNotification(
        NOTIFICATION_TYPES.ERROR,
        'Upload Failed',
        `Failed to upload template background image: ${uploadError.message}`,
        { persistent: true }
      );
      notificationService.add(errorNotification);

      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`
      };
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('template-backgrounds')
      .getPublicUrl(fileName);

    // Get image dimensions
    const dimensions = await getImageDimensions(imageFile);

    prodLogger.debug('✅ Template Service: Image uploaded successfully', { fileName, publicUrl });

    // Show success notification
    const successNotification = createTemplateNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Image Uploaded',
      `Template background image "${imageFile.name}" uploaded successfully.`,
      { duration: 3000 }
    );
    notificationService.add(successNotification);

    return {
      success: true,
      url: publicUrl,
      fileName,
      dimensions
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Image upload failed:', error);
    errorMonitor.captureError(
      new Error(`Image upload failed: ${error.message}`),
      { templateId },
      ErrorSeverity.HIGH
    );

    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get image dimensions from file
 * @param {File} imageFile - Image file
 * @returns {Promise<Object>} Image dimensions
 */
const getImageDimensions = (imageFile) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    img.onerror = () => {
      resolve({
        width: 1200, // Default dimensions
        height: 1600
      });
    };
    img.src = URL.createObjectURL(imageFile);
  });
};

/**
 * Create a new template
 * @param {Object} templateData - Template data
 * @returns {Promise<Object>} Creation result
 */
export const createTemplate = async (templateData) => {
  try {
    prodLogger.debug('📋 Template Service: Creating new template', { name: templateData.name });

    // Validate template data
    const validation = validateTemplateData(templateData);
    if (!validation.isValid) {
      return {
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      };
    }

    // Generate template ID
    const templateId = `template-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Prepare template data for database
    const dbTemplateData = {
      id: templateId,
      name: templateData.name.trim(),
      description: templateData.description?.trim() || null,
      category: templateData.category.trim(),
      tags: templateData.tags || [],
      background_image_url: templateData.background_image_url,
      background_image_width: templateData.background_image_width,
      background_image_height: templateData.background_image_height,
      text_overlays: templateData.text_overlays,
      supported_formats: templateData.supported_formats || ['pdf', 'png', 'jpg'],
      is_premium: templateData.is_premium || false,
      status: templateData.status || 'active',
      usage_count: 0,
      rating: 0.0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Insert into database
    const { data: createdTemplate, error: dbError } = await supabase
      .from('cover_templates')
      .insert([dbTemplateData])
      .select()
      .single();

    if (dbError) {
      prodLogger.error('❌ Template Service: Failed to create template:', dbError);
      errorMonitor.captureError(
        new Error(`Template creation failed: ${dbError.message}`),
        { templateData: dbTemplateData },
        ErrorSeverity.HIGH
      );

      // Show error notification
      const errorNotification = createTemplateNotification(
        NOTIFICATION_TYPES.ERROR,
        'Template Creation Failed',
        `Failed to create template "${templateData.name}": ${dbError.message}`,
        { persistent: true }
      );
      notificationService.add(errorNotification);

      return {
        success: false,
        error: `Failed to create template: ${dbError.message}`
      };
    }

    // Clear cache to ensure fresh data
    clearTemplateCache();

    prodLogger.debug('✅ Template Service: Template created successfully', { id: templateId });

    // Show success notification
    const successNotification = createTemplateNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Template Created',
      `Template "${templateData.name}" has been created successfully.`,
      { duration: 4000 }
    );
    notificationService.add(successNotification);

    return {
      success: true,
      template: createdTemplate
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Template creation failed:', error);
    errorMonitor.captureError(
      new Error(`Template creation failed: ${error.message}`),
      { templateData },
      ErrorSeverity.HIGH
    );

    // Show error notification
    const errorNotification = createTemplateNotification(
      NOTIFICATION_TYPES.ERROR,
      'Template Creation Failed',
      `An error occurred while creating template "${templateData?.name || 'Unknown'}": ${error.message}`,
      { persistent: true }
    );
    notificationService.add(errorNotification);

    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get default text overlay configurations
 * @param {string} layoutType - Layout type (standard, minimal, professional, custom)
 * @returns {Object} Default text overlays configuration
 */
export const getDefaultTextOverlays = (layoutType = 'standard') => {
  const layouts = {
    standard: {
      overlays: [
        {
          id: "title",
          type: "text",
          placeholder: "{{title}}",
          position: { x: 50, y: 200, width: 500, height: 80 },
          styling: {
            fontSize: 36,
            fontFamily: "Arial",
            fontWeight: "bold",
            color: "#000000",
            textAlign: "center",
            lineHeight: 1.2,
            maxLines: 2,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        },
        {
          id: "author",
          type: "text",
          placeholder: "by {{author}}",
          position: { x: 50, y: 320, width: 500, height: 40 },
          styling: {
            fontSize: 20,
            fontFamily: "Arial",
            fontWeight: "normal",
            color: "#666666",
            textAlign: "center",
            lineHeight: 1.2,
            maxLines: 1,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        },
        {
          id: "description",
          type: "text",
          placeholder: "{{description}}",
          position: { x: 50, y: 400, width: 500, height: 120 },
          styling: {
            fontSize: 14,
            fontFamily: "Arial",
            fontWeight: "normal",
            color: "#333333",
            textAlign: "center",
            lineHeight: 1.4,
            maxLines: 6,
            overflow: "ellipsis",
            verticalAlign: "top"
          }
        }
      ]
    },
    minimal: {
      overlays: [
        {
          id: "title",
          type: "text",
          placeholder: "{{title}}",
          position: { x: 100, y: 300, width: 400, height: 100 },
          styling: {
            fontSize: 42,
            fontFamily: "Arial",
            fontWeight: "bold",
            color: "#FFFFFF",
            textAlign: "center",
            lineHeight: 1.1,
            maxLines: 3,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        }
      ]
    },
    professional: {
      overlays: [
        {
          id: "title",
          type: "text",
          placeholder: "{{title}}",
          position: { x: 80, y: 150, width: 440, height: 120 },
          styling: {
            fontSize: 32,
            fontFamily: "Georgia",
            fontWeight: "bold",
            color: "#2c3e50",
            textAlign: "left",
            lineHeight: 1.3,
            maxLines: 3,
            overflow: "ellipsis",
            verticalAlign: "top"
          }
        },
        {
          id: "author",
          type: "text",
          placeholder: "{{author}}",
          position: { x: 80, y: 300, width: 440, height: 40 },
          styling: {
            fontSize: 18,
            fontFamily: "Georgia",
            fontWeight: "normal",
            color: "#7f8c8d",
            textAlign: "left",
            lineHeight: 1.2,
            maxLines: 1,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        },
        {
          id: "subtitle",
          type: "text",
          placeholder: "{{subtitle}}",
          position: { x: 80, y: 350, width: 440, height: 60 },
          styling: {
            fontSize: 16,
            fontFamily: "Georgia",
            fontWeight: "italic",
            color: "#95a5a6",
            textAlign: "left",
            lineHeight: 1.4,
            maxLines: 2,
            overflow: "ellipsis",
            verticalAlign: "top"
          }
        }
      ]
    },
    custom: {
      overlays: [
        {
          id: "title",
          type: "text",
          placeholder: "{{title}}",
          position: { x: 50, y: 200, width: 500, height: 100 },
          styling: {
            fontSize: 40,
            fontFamily: "Arial",
            fontWeight: "bold",
            color: "#000000",
            textAlign: "center",
            lineHeight: 1.2,
            maxLines: 2,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        },
        {
          id: "author",
          type: "text",
          placeholder: "by {{author}}",
          position: { x: 50, y: 320, width: 500, height: 50 },
          styling: {
            fontSize: 22,
            fontFamily: "Arial",
            fontWeight: "normal",
            color: "#666666",
            textAlign: "center",
            lineHeight: 1.2,
            maxLines: 1,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        }
      ]
    }
  };

  return layouts[layoutType] || layouts.standard;
};

/**
 * Delete template and its associated background image
 * @param {string} templateId - Template ID to delete
 * @returns {Promise<Object>} Deletion result
 */
export const deleteTemplate = async (templateId) => {
  try {
    prodLogger.debug('🗑️ Template Service: Deleting template', { templateId });

    // Step 1: Get template data to access background image URL
    const { data: template, error: fetchError } = await supabase
      .from('cover_templates')
      .select('id, name, background_image_url')
      .eq('id', templateId)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // Step 2: Delete background image from storage if it exists
    if (template.background_image_url) {
      try {
        // Extract filename from the public URL
        // URLs look like: https://[project].supabase.co/storage/v1/object/public/template-backgrounds/filename.ext
        const url = new URL(template.background_image_url);
        const pathSegments = url.pathname.split('/');
        const fileName = pathSegments[pathSegments.length - 1]; // Get the last segment (filename)

        if (fileName && fileName.length > 0) {
          const { error: storageError } = await supabase.storage
            .from('template-backgrounds')
            .remove([fileName]);

          if (storageError) {
            prodLogger.warn('⚠️ Template Service: Failed to delete background from storage:', storageError);
            // Continue with database deletion even if storage deletion fails
          } else {
            prodLogger.debug('✅ Template Service: Background image deleted from storage:', fileName);
          }
        }
      } catch (urlError) {
        prodLogger.warn('⚠️ Template Service: Failed to parse background image URL for cleanup:', urlError);
        // Continue with database deletion even if storage cleanup fails
      }
    }

    // Step 3: Delete template record from database
    const { error: dbError } = await supabase
      .from('cover_templates')
      .delete()
      .eq('id', templateId);

    if (dbError) {
      throw dbError;
    }

    // Step 4: Clear cache to ensure fresh data
    clearTemplateCache();

    prodLogger.debug('✅ Template Service: Template deleted successfully', { templateId, templateName: template.name });

    // Show success notification
    const successNotification = createTemplateNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Template Deleted',
      `Template "${template.name}" and its background image deleted successfully.`,
      { duration: 3000 }
    );
    notificationService.add(successNotification);

    return {
      success: true,
      message: 'Template deleted successfully'
    };

  } catch (error) {
    prodLogger.error('❌ Template Service: Template deletion failed:', error);
    errorMonitor.captureError(
      new Error(`Template deletion failed: ${error.message}`),
      { templateId },
      ErrorSeverity.HIGH
    );

    // Show error notification
    const errorNotification = createTemplateNotification(
      NOTIFICATION_TYPES.ERROR,
      'Deletion Failed',
      `Failed to delete template: ${error.message}`,
      { persistent: true }
    );
    notificationService.add(errorNotification);

    return {
      success: false,
      error: error.message
    };
  }
};

export default {
  fetchCoverTemplates,
  fetchTemplateById,
  fetchTemplateCategories,
  searchTemplates,
  incrementTemplateUsage,
  clearTemplateCache,
  validateTemplateData,
  uploadBackgroundImage,
  createTemplate,
  deleteTemplate,
  getDefaultTextOverlays
};
