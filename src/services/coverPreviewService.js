/**
 * Cover Preview Service for DocForge AI
 * Handles cover-only preview generation separate from full document preview
 *
 * Features:
 * - Generate cover-only previews for template selection
 * - Fast template switching without full document processing
 * - Lightweight preview generation for better performance
 */

import imageOverlayService from "./imageOverlayService.js";
import overlayPreviewService from "./overlayPreviewService.js";
import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor.js";

import { prodLogger } from '../utils/prodLogger.js';
/**
 * Extract document title from various possible sources
 * @param {Object} documentData - Document questionnaire data
 * @param {Object} generatedContent - AI-generated content
 * @returns {string} Document title
 */
const extractDocumentTitle = (documentData, generatedContent) => {
  // Check for title in various possible locations (same logic as DocumentPublish)
  if (
    documentData?.titleSelection?.selectedTitle &&
    documentData.titleSelection.selectedTitle !== "custom"
  ) {
    return documentData.titleSelection.selectedTitle;
  }
  if (documentData?.titleSelection?.customTitle) {
    return documentData.titleSelection.customTitle;
  }
  if (documentData?.title) {
    return documentData.title;
  }
  if (generatedContent?.title) {
    return generatedContent.title;
  }

  // Fallback: generate title from topic
  if (documentData?.topicAndNiche?.mainTopic) {
    return `Guide to ${documentData.topicAndNiche.mainTopic}`;
  }

  return "Untitled Document";
};

/**
 * Extract document author from user profile
 * @param {Object} userProfile - User profile data
 * @param {Object} user - User authentication data
 * @param {Object} documentData - Document data (for any existing author override)
 * @returns {string} Document author
 */
const extractDocumentAuthor = (userProfile, user, documentData) => {
  // First check if author is already set in document data
  if (documentData?.author) {
    return documentData.author;
  }

  // Use authenticated user's name
  if (userProfile?.full_name) {
    return userProfile.full_name;
  }
  if (userProfile?.first_name && userProfile?.last_name) {
    return `${userProfile.first_name} ${userProfile.last_name}`;
  }
  if (userProfile?.first_name) {
    return userProfile.first_name;
  }
  if (user?.email) {
    return user.email.split("@")[0]; // Use email username as fallback
  }

  return "Unknown Author";
};

/**
 * Generate enhanced document data for cover preview
 * Uses proper data extraction from questionnaire data and user profile
 * @param {Object} documentData - Document questionnaire data
 * @param {Object} generatedContent - AI-generated content
 * @param {Object} userProfile - User profile data
 * @param {Object} user - User authentication data
 * @returns {Object} Enhanced document data for template rendering
 */
const generateCoverDocumentData = (
  documentData,
  generatedContent = null,
  userProfile = null,
  user = null
) => {
  return {
    title: extractDocumentTitle(documentData, generatedContent),
    author: extractDocumentAuthor(userProfile, user, documentData),
    description: documentData?.description || "",
    // Add placeholder values for template rendering
    wordCount: "TBD",
    pageCount: "TBD",
    readTime: "TBD",
    createdAt: documentData?.createdAt || new Date().toISOString(),
    updatedAt: documentData?.updatedAt || new Date().toISOString(),
  };
};

/**
 * Generate cover-only preview for template selection
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document questionnaire data
 * @param {Object} options - Preview options
 * @param {Object} options.generatedContent - AI-generated content (optional)
 * @param {Object} options.userProfile - User profile data (optional)
 * @param {Object} options.user - User authentication data (optional)
 * @param {string} options.format - Image format ('png', 'jpeg', 'webp')
 * @param {number} options.quality - Image quality (0.1 to 1.0)
 * @returns {Promise<Object>} Cover preview data
 */
export const generateCoverPreview = async (
  template,
  documentData,
  options = {}
) => {
  try {
    const {
      generatedContent = null,
      userProfile = null,
      user = null,
      quality = 0.9,
      format = "png",
    } = options;

    prodLogger.debug("🎨 Generating cover-only preview", {
      templateId: template.id,
      templateName: template.name,
      hasGeneratedContent: !!generatedContent,
      hasUserProfile: !!userProfile,
      hasCustomCover: documentData?.contentDetails?.customCoverImage?.enabled,
    });

    // Check if custom cover image should be used
    const customCoverConfig = documentData?.contentDetails?.customCoverImage;
    if (customCoverConfig?.enabled && customCoverConfig?.imageUrl) {
      return await generateCustomCoverPreview(customCoverConfig, documentData, {
        quality,
        format,
      });
    }

    // Generate enhanced document data for cover with proper data extraction
    const enhancedDocumentData = generateCoverDocumentData(
      documentData,
      generatedContent,
      userProfile,
      user
    );

    // Include logo data if available
    if (documentData?.logo) {
      enhancedDocumentData.logo = documentData.logo;
    }

    // Generate cover using image overlay service
    const templateCanvas = await imageOverlayService.renderTemplate(
      template,
      enhancedDocumentData
    );
    const coverImageData = imageOverlayService.exportAsImage(
      templateCanvas,
      format,
      quality
    );

    // FULL-BLEED: Create cover HTML for preview display - edge-to-edge, no background
    const coverHTML = `<div style="width: 100%; height: 100%; margin: 0; padding: 0; background: white; overflow: hidden; display: flex; align-items: center; justify-content: center;">
      <img src="${coverImageData}" alt="Document Cover" style="width: 100%; height: 100%; object-fit: cover; display: block; margin: 0; padding: 0;" />
    </div>`;

    prodLogger.debug("✅ Cover preview generated successfully");

    return {
      coverHTML,
      coverImageData,
      metadata: {
        templateId: template.id,
        templateName: template.name,
        templateCategory: template.category,
        documentData: enhancedDocumentData,
        previewType: "cover-only",
        generatedAt: new Date().toISOString(),
      },
    };
  } catch (error) {
    prodLogger.error("❌ Error generating cover preview:", error);
    errorMonitor.captureError(
      error,
      {
        templateId: template?.id,
        documentTitle: documentData?.title,
      },
      ErrorSeverity.MEDIUM
    );

    // Return fallback cover preview
    return {
      coverHTML: generateFallbackCoverHTML(
        template,
        documentData,
        generatedContent,
        userProfile,
        user
      ),
      coverImageData: null,
      metadata: {
        templateId: template?.id,
        templateName: template?.name,
        error: error.message,
        previewType: "cover-only-fallback",
        generatedAt: new Date().toISOString(),
      },
    };
  }
};

/**
 * Generate fallback cover HTML when template rendering fails
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document questionnaire data
 * @param {Object} generatedContent - AI-generated content (optional)
 * @param {Object} userProfile - User profile data (optional)
 * @param {Object} user - User authentication data (optional)
 * @returns {string} Fallback cover HTML
 */
const generateFallbackCoverHTML = (
  template,
  documentData,
  generatedContent = null,
  userProfile = null,
  user = null
) => {
  const enhancedDocumentData = generateCoverDocumentData(
    documentData,
    generatedContent,
    userProfile,
    user
  );

  return `<div class="fallback-cover" style="
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    box-sizing: border-box;
  ">
    <h1 style="font-size: 2.5em; margin-bottom: 20px; font-weight: bold; text-align: center;">${
      enhancedDocumentData.title
    }</h1>
    ${
      enhancedDocumentData.author
        ? `<div style="font-size: 1.2em; margin-bottom: 20px; opacity: 0.9; text-align: center;">by ${enhancedDocumentData.author}</div>`
        : ""
    }
    ${
      enhancedDocumentData.description
        ? `<p style="font-size: 1em; line-height: 1.5; opacity: 0.8; max-width: 600px; margin: 0 auto; text-align: center;">${enhancedDocumentData.description}</p>`
        : ""
    }
    <p style="font-size: 0.8em; margin-top: 30px; opacity: 0.6; text-align: center;">Template preview unavailable - using fallback design</p>
  </div>`;
};

/**
 * Update cover preview with new template
 * @param {Object} newTemplate - New template configuration
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Preview options
 * @returns {Promise<Object>} Updated cover preview data
 */
export const updateCoverPreview = async (
  newTemplate,
  documentData,
  options = {}
) => {
  try {
    prodLogger.debug("🔄 Updating cover preview with new template", {
      newTemplateId: newTemplate.id,
      newTemplateName: newTemplate.name,
    });

    // Generate new cover preview
    const newCoverPreview = await generateCoverPreview(
      newTemplate,
      documentData,
      options
    );

    prodLogger.debug("✅ Cover preview updated successfully");
    return newCoverPreview;
  } catch (error) {
    prodLogger.error("❌ Failed to update cover preview:", error);
    throw error;
  }
};

/**
 * Generate cover thumbnail for template cards
 * @param {Object} template - Template configuration
 * @param {Object} sampleData - Sample document data for preview
 * @returns {Promise<string|null>} Base64 image data or null if failed
 */
export const generateCoverThumbnail = async (template, sampleData = null) => {
  try {
    const defaultSampleData = {
      title: "Sample Document Title",
      author: "Author Name",
      description:
        "This is how your document description will appear on the cover.",
    };

    const thumbnailData = sampleData || defaultSampleData;
    const enhancedData = generateCoverDocumentData(
      thumbnailData,
      null,
      null,
      null
    );

    // Generate thumbnail using image overlay service
    const thumbnailCanvas = await imageOverlayService.renderTemplate(
      template,
      enhancedData
    );
    const thumbnailImage = imageOverlayService.exportAsImage(
      thumbnailCanvas,
      "png",
      0.7
    );

    prodLogger.debug(`✅ Cover thumbnail generated for template: ${template.name}`);
    return thumbnailImage;
  } catch (error) {
    prodLogger.error("Error generating cover thumbnail:", error);
    errorMonitor.captureError(
      error,
      { template: template?.id },
      ErrorSeverity.MEDIUM
    );

    // Return null if thumbnail generation fails
    return null;
  }
};

/**
 * Batch generate cover previews for multiple templates
 * @param {Array} templates - Array of template configurations
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Preview options
 * @returns {Promise<Array>} Array of cover preview results
 */
export const batchGenerateCoverPreviews = async (
  templates,
  documentData,
  options = {}
) => {
  try {
    prodLogger.debug(
      `🎨 Batch generating cover previews for ${templates.length} templates`
    );

    const previews = await Promise.allSettled(
      templates.map((template) =>
        generateCoverPreview(template, documentData, options)
      )
    );

    const results = previews.map((result, index) => ({
      templateId: templates[index].id,
      success: result.status === "fulfilled",
      data: result.status === "fulfilled" ? result.value : null,
      error: result.status === "rejected" ? result.reason.message : null,
    }));

    const successCount = results.filter((r) => r.success).length;
    prodLogger.debug(
      `✅ Batch cover preview generation completed: ${successCount}/${templates.length} successful`
    );

    return results;
  } catch (error) {
    prodLogger.error("❌ Batch cover preview generation failed:", error);
    throw error;
  }
};

/**
 * Generate cover preview using custom uploaded image
 * @param {Object} customCoverConfig - Custom cover image configuration
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Preview options
 * @returns {Promise<Object>} Custom cover preview data
 */
const generateCustomCoverPreview = async (
  customCoverConfig,
  documentData,
  options = {}
) => {
  try {
    const { quality = 0.9, format = "png" } = options;

    prodLogger.debug("🖼️ Generating custom cover preview", {
      imageUrl: customCoverConfig.imageUrl,
      documentTitle: documentData?.title,
    });

    // Create cover HTML using the custom image
    const coverHTML = `<img src="${customCoverConfig.imageUrl}" alt="Custom Document Cover" style="width: 100%; height: 100%; object-fit: cover; display: block;" />`;

    prodLogger.debug("✅ Custom cover preview generated successfully");

    return {
      coverHTML,
      coverImageData: customCoverConfig.imageUrl, // Use the uploaded image URL directly
      metadata: {
        templateId: "custom-cover",
        templateName: "Custom Cover Image",
        templateCategory: "custom",
        documentData: {
          title: extractDocumentTitle(documentData),
          author: extractDocumentAuthor(null, null, documentData),
          description: documentData?.description || "",
        },
        customCoverMetadata: customCoverConfig.imageMetadata,
        previewType: "custom-cover",
        generatedAt: new Date().toISOString(),
      },
    };
  } catch (error) {
    prodLogger.error("❌ Error generating custom cover preview:", error);
    errorMonitor.captureError(
      error,
      {
        customImageUrl: customCoverConfig?.imageUrl,
        documentTitle: documentData?.title,
      },
      ErrorSeverity.MEDIUM
    );

    // Fallback to simple HTML cover if custom cover fails
    return {
      coverHTML: generateFallbackCoverHTML(null, documentData),
      coverImageData: null,
      metadata: {
        templateId: "custom-cover-fallback",
        templateName: "Custom Cover (Fallback)",
        error: error.message,
        previewType: "custom-cover-fallback",
        generatedAt: new Date().toISOString(),
      },
    };
  }
};

/**
 * Fetch template by ID for fallback purposes
 * @param {string} templateId - Template ID to fetch
 * @returns {Promise<Object|null>} Template object or null if not found
 */
const fetchTemplateById = async (templateId) => {
  try {
    // This would typically fetch from Supabase
    // For now, return null to indicate template not found
    prodLogger.debug("🔍 Fetching fallback template:", templateId);
    return null;
  } catch (error) {
    prodLogger.error("❌ Error fetching fallback template:", error);
    return null;
  }
};

/**
 * Enhanced error handling for cover generation
 * @param {Error} error - The error that occurred
 * @param {Object} context - Error context information
 * @returns {Object} Standardized error response
 */
const handleCoverGenerationError = (error, context = {}) => {
  const errorTypes = {
    NETWORK_ERROR: "network",
    IMAGE_LOAD_ERROR: "image_load",
    TEMPLATE_ERROR: "template",
    STORAGE_ERROR: "storage",
    VALIDATION_ERROR: "validation",
    UNKNOWN_ERROR: "unknown",
  };

  let errorType = errorTypes.UNKNOWN_ERROR;
  let userMessage = "An unexpected error occurred while generating the cover.";

  // Categorize error types
  if (error.message?.includes("network") || error.message?.includes("fetch")) {
    errorType = errorTypes.NETWORK_ERROR;
    userMessage = "Network error. Please check your connection and try again.";
  } else if (
    error.message?.includes("image") ||
    error.message?.includes("load")
  ) {
    errorType = errorTypes.IMAGE_LOAD_ERROR;
    userMessage =
      "Failed to load the cover image. Please try a different image.";
  } else if (error.message?.includes("template")) {
    errorType = errorTypes.TEMPLATE_ERROR;
    userMessage = "Template error. Please try a different template.";
  } else if (
    error.message?.includes("storage") ||
    error.message?.includes("upload")
  ) {
    errorType = errorTypes.STORAGE_ERROR;
    userMessage = "Storage error. Please try uploading the image again.";
  } else if (
    error.message?.includes("validation") ||
    error.message?.includes("invalid")
  ) {
    errorType = errorTypes.VALIDATION_ERROR;
    userMessage = "Invalid image format. Please use JPG, PNG, or WebP files.";
  }

  return {
    errorType,
    userMessage,
    technicalMessage: error.message,
    context,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Generate cover preview with text overlay customizations
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document metadata
 * @param {Object} customizations - User customizations for text overlays
 * @param {Object} options - Generation options
 * @returns {Promise<Object>} Cover preview data with customizations
 */
const generateCoverPreviewWithCustomizations = async (
  template,
  documentData,
  customizations = {},
  options = {}
) => {
  try {
    prodLogger.debug("🎨 Generating cover preview with customizations", {
      templateId: template.id,
      customizationCount: Object.keys(customizations).length,
    });

    // Extract proper document data with title/author extraction
    const { generatedContent, userProfile, user, ...otherOptions } = options;
    const enhancedDocumentData = generateCoverDocumentData(
      documentData,
      generatedContent,
      userProfile,
      user
    );

    // Use overlay preview service for customized previews with enhanced data
    return await overlayPreviewService.generatePreviewWithCustomizations(
      template,
      enhancedDocumentData,
      customizations,
      otherOptions
    );
  } catch (error) {
    prodLogger.error("Error generating cover preview with customizations:", error);
    errorMonitor.captureError(
      error,
      { template: template?.id, customizations },
      ErrorSeverity.MEDIUM
    );
    throw error;
  }
};

export default {
  generateCoverPreview,
  updateCoverPreview,
  generateCoverThumbnail,
  batchGenerateCoverPreviews,
  generateCustomCoverPreview,
  generateCoverPreviewWithCustomizations,
};
