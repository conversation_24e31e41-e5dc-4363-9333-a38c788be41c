import React, { useState, useRef } from 'react';
import Button from './Button';
import Icon from './Icon';
import { validateLogo } from '../../services/logoService';
import { prodLogger } from '../../utils/prodLogger.js';

/**
 * Logo Upload Component
 * Provides drag-and-drop and click-to-upload functionality for logo files
 * Follows the existing design patterns from DocxUpload and PdfUpload components
 */
const LogoUpload = ({ 
  onFileSelect, 
  onValidationChange,
  disabled = false,
  className = "",
  showPreview = true,
  maxSize = "10MB",
  recommendedSize = "2MB"
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [validation, setValidation] = useState({ isValid: false });
  const fileInputRef = useRef(null);

  // Handle drag events
  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = (file) => {
    prodLogger.debug('🖼️ Logo file selected:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Validate the file
    const validationResult = validateLogo(file);
    
    setValidation(validationResult);
    setSelectedFile(file);

    // Create preview URL for valid images
    if (validationResult.success && file.type.startsWith('image/')) {
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);
    } else {
      setPreviewUrl(null);
    }

    // Notify parent components
    if (onFileSelect) {
      onFileSelect(file, validationResult);
    }
    if (onValidationChange) {
      onValidationChange(validationResult);
    }
  };

  const triggerFileInput = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setValidation({ isValid: false });
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    if (onFileSelect) {
      onFileSelect(null, { isValid: false });
    }
    if (onValidationChange) {
      onValidationChange({ isValid: false });
    }
  };

  const getDropzoneClasses = () => {
    const baseClasses = "relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer";
    
    if (disabled) {
      return `${baseClasses} border-border-muted bg-background-muted cursor-not-allowed opacity-50`;
    }
    
    if (validation.isValid) {
      return `${baseClasses} border-green-300 bg-green-50 hover:bg-green-100`;
    }
    
    if (validation.errors?.length > 0) {
      return `${baseClasses} border-red-300 bg-red-50 hover:bg-red-100`;
    }
    
    if (isDragOver) {
      return `${baseClasses} border-primary bg-primary-light`;
    }
    
    return `${baseClasses} border-border hover:border-primary hover:bg-background-subtle`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Drop zone */}
      <div
        className={getDropzoneClasses()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={triggerFileInput}
      >
        <div className="space-y-4">
          {/* Icon and main message */}
          <div className="space-y-3">
            {selectedFile && validation.isValid ? (
              <Icon name="CheckCircle" size={48} className="mx-auto text-green-500" />
            ) : validation.errors?.length > 0 ? (
              <Icon name="AlertCircle" size={48} className="mx-auto text-red-500" />
            ) : (
              <Icon name="Image" size={48} className="mx-auto text-text-secondary" />
            )}

            <div>
              <h3 className="text-lg font-medium text-text-primary">
                {selectedFile ? selectedFile.name : "Upload your logo"}
              </h3>
              <p className="text-sm text-text-secondary mt-1">
                {selectedFile 
                  ? `${(selectedFile.size / (1024 * 1024)).toFixed(2)} MB`
                  : "Drag and drop your logo file here, or click to browse"
                }
              </p>
            </div>
          </div>

          {/* Preview */}
          {showPreview && previewUrl && (
            <div className="mt-4">
              <img 
                src={previewUrl} 
                alt="Logo preview" 
                className="mx-auto max-w-32 max-h-24 object-contain rounded border"
              />
            </div>
          )}

          {/* Validation messages */}
          {validation.errors?.length > 0 && (
            <div className="text-sm text-red-600 space-y-1">
              {validation.errors.map((error, index) => (
                <p key={index}>• {error}</p>
              ))}
            </div>
          )}

          {validation.warnings?.length > 0 && (
            <div className="text-sm text-yellow-600 space-y-1">
              {validation.warnings.map((warning, index) => (
                <p key={index}>⚠️ {warning}</p>
              ))}
            </div>
          )}

          {/* File requirements */}
          <div className="text-xs text-text-muted space-y-1">
            <p>• Supported formats: JPEG, PNG, WebP</p>
            <p>• Maximum file size: {maxSize}</p>
            <p>• Recommended size: {recommendedSize} or smaller</p>
            <p>• Best dimensions: 800x800px or smaller</p>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 justify-center">
            {!selectedFile ? (
              <Button
                variant="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  triggerFileInput();
                }}
                disabled={disabled}
                iconName="Upload"
                iconPosition="left"
              >
                Choose Logo File
              </Button>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    triggerFileInput();
                  }}
                  disabled={disabled}
                  iconName="RefreshCw"
                  iconPosition="left"
                  size="sm"
                >
                  Change File
                </Button>
                <Button
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    clearSelection();
                  }}
                  disabled={disabled}
                  iconName="X"
                  iconPosition="left"
                  size="sm"
                >
                  Remove
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Success message */}
      {selectedFile && validation.isValid && (
        <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-3 rounded-lg">
          <Icon name="CheckCircle" size={16} />
          <span>Logo ready for upload! Click "Save Logo" to add it to your collection.</span>
        </div>
      )}
    </div>
  );
};

export default LogoUpload;
